import { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
    Search, 
    TrendingUp, 
    User, 
    Users, 
    FileText, 
    Clock,
    X
} from 'lucide-react';

interface SearchResult {
    posts: any[];
    users: any[];
    organizations: any[];
    total: number;
}

interface Suggestion {
    suggestion: string;
    type: 'post' | 'organization' | 'user';
}

interface AdvancedSearchProps {
    onPostSelect?: (post: any) => void;
    onUserSelect?: (user: any) => void;
    onOrganizationSelect?: (organization: any) => void;
    placeholder?: string;
    showResults?: boolean;
}

export default function AdvancedSearch({ 
    onPostSelect, 
    onUserSelect, 
    onOrganizationSelect,
    placeholder = "Search posts, users, organizations...",
    showResults = true
}: AdvancedSearchProps) {
    const [query, setQuery] = useState('');
    const [results, setResults] = useState<SearchResult>({ posts: [], users: [], organizations: [], total: 0 });
    const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
    const [trending, setTrending] = useState<any>({ types: [], organizations: [] });
    const [loading, setLoading] = useState(false);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [recentSearches, setRecentSearches] = useState<string[]>([]);
    
    const searchRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    // Load recent searches from localStorage
    useEffect(() => {
        const saved = localStorage.getItem('unilink_recent_searches');
        if (saved) {
            setRecentSearches(JSON.parse(saved));
        }
    }, []);

    // Load trending data
    useEffect(() => {
        const loadTrending = async () => {
            try {
                const response = await fetch('/api/v1/search/trending', {
                    headers: {
                        'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                        'Accept': 'application/json',
                    }
                });
                if (response.ok) {
                    const data = await response.json();
                    setTrending(data);
                }
            } catch (error) {
                console.error('Error loading trending:', error);
            }
        };

        loadTrending();
    }, []);

    // Handle search
    const performSearch = async (searchQuery: string) => {
        if (!searchQuery.trim()) {
            setResults({ posts: [], users: [], organizations: [], total: 0 });
            return;
        }

        setLoading(true);
        try {
            const response = await fetch(`/api/v1/search/global?q=${encodeURIComponent(searchQuery)}`, {
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'Accept': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                setResults(data);
                
                // Save to recent searches
                const newRecent = [searchQuery, ...recentSearches.filter(s => s !== searchQuery)].slice(0, 5);
                setRecentSearches(newRecent);
                localStorage.setItem('unilink_recent_searches', JSON.stringify(newRecent));
            }
        } catch (error) {
            console.error('Error searching:', error);
        } finally {
            setLoading(false);
        }
    };

    // Handle suggestions
    const loadSuggestions = async (searchQuery: string) => {
        if (searchQuery.length < 2) {
            setSuggestions([]);
            return;
        }

        try {
            const response = await fetch(`/api/v1/search/suggestions?q=${encodeURIComponent(searchQuery)}`, {
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'Accept': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                setSuggestions(data);
            }
        } catch (error) {
            console.error('Error loading suggestions:', error);
        }
    };

    // Debounced search
    useEffect(() => {
        const timer = setTimeout(() => {
            if (query) {
                performSearch(query);
                loadSuggestions(query);
            } else {
                setResults({ posts: [], users: [], organizations: [], total: 0 });
                setSuggestions([]);
            }
        }, 300);

        return () => clearTimeout(timer);
    }, [query]);

    // Handle click outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
                setShowSuggestions(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleSuggestionClick = (suggestion: string) => {
        setQuery(suggestion);
        setShowSuggestions(false);
        performSearch(suggestion);
    };

    const clearSearch = () => {
        setQuery('');
        setResults({ posts: [], users: [], organizations: [], total: 0 });
        setSuggestions([]);
        inputRef.current?.focus();
    };

    const getTypeIcon = (type: string) => {
        switch (type) {
            case 'post': return <FileText className="w-4 h-4" />;
            case 'user': return <User className="w-4 h-4" />;
            case 'organization': return <Users className="w-4 h-4" />;
            default: return <Search className="w-4 h-4" />;
        }
    };

    return (
        <div ref={searchRef} className="relative w-full max-w-2xl">
            {/* Search Input */}
            <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                    ref={inputRef}
                    type="text"
                    placeholder={placeholder}
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    onFocus={() => setShowSuggestions(true)}
                    className="pl-10 pr-10"
                />
                {query && (
                    <button
                        onClick={clearSearch}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                        <X className="w-4 h-4" />
                    </button>
                )}
            </div>

            {/* Suggestions Dropdown */}
            {showSuggestions && (query || recentSearches.length > 0 || trending.types.length > 0) && (
                <Card className="absolute top-full left-0 right-0 mt-1 z-50 max-h-96 overflow-y-auto">
                    <CardContent className="p-0">
                        {/* Recent Searches */}
                        {!query && recentSearches.length > 0 && (
                            <div className="p-3 border-b">
                                <div className="flex items-center gap-2 mb-2">
                                    <Clock className="w-4 h-4 text-gray-500" />
                                    <span className="text-sm font-medium text-gray-700">Recent Searches</span>
                                </div>
                                <div className="flex flex-wrap gap-1">
                                    {recentSearches.map((search, index) => (
                                        <Badge
                                            key={index}
                                            variant="outline"
                                            className="cursor-pointer hover:bg-gray-100"
                                            onClick={() => handleSuggestionClick(search)}
                                        >
                                            {search}
                                        </Badge>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Trending */}
                        {!query && trending.types.length > 0 && (
                            <div className="p-3 border-b">
                                <div className="flex items-center gap-2 mb-2">
                                    <TrendingUp className="w-4 h-4 text-gray-500" />
                                    <span className="text-sm font-medium text-gray-700">Trending</span>
                                </div>
                                <div className="space-y-1">
                                    {trending.types.map((type: any, index: number) => (
                                        <button
                                            key={index}
                                            onClick={() => handleSuggestionClick(type.type)}
                                            className="flex items-center justify-between w-full p-2 text-left hover:bg-gray-50 rounded"
                                        >
                                            <span className="capitalize">{type.type}</span>
                                            <Badge variant="secondary">{type.count}</Badge>
                                        </button>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Suggestions */}
                        {query && suggestions.length > 0 && (
                            <div className="p-3">
                                <div className="space-y-1">
                                    {suggestions.map((suggestion, index) => (
                                        <button
                                            key={index}
                                            onClick={() => handleSuggestionClick(suggestion.suggestion)}
                                            className="flex items-center gap-3 w-full p-2 text-left hover:bg-gray-50 rounded"
                                        >
                                            {getTypeIcon(suggestion.type)}
                                            <span>{suggestion.suggestion}</span>
                                            <Badge variant="outline" className="ml-auto">
                                                {suggestion.type}
                                            </Badge>
                                        </button>
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            )}

            {/* Search Results */}
            {showResults && query && results.total > 0 && (
                <Card className="mt-4">
                    <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                            <span>Search Results</span>
                            <Badge>{results.total} found</Badge>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {/* Posts */}
                        {results.posts.length > 0 && (
                            <div>
                                <h3 className="font-medium mb-3 flex items-center gap-2">
                                    <FileText className="w-4 h-4" />
                                    Posts ({results.posts.length})
                                </h3>
                                <div className="space-y-2">
                                    {results.posts.slice(0, 3).map((post) => (
                                        <div
                                            key={post.id}
                                            onClick={() => onPostSelect?.(post)}
                                            className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                                        >
                                            <h4 className="font-medium">{post.title}</h4>
                                            <p className="text-sm text-gray-600 line-clamp-2">{post.content}</p>
                                            <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
                                                <span>{post.user.name}</span>
                                                {post.organization && (
                                                    <>
                                                        <span>•</span>
                                                        <span>{post.organization.name}</span>
                                                    </>
                                                )}
                                                <span>•</span>
                                                <span>{new Date(post.created_at).toLocaleDateString()}</span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Users */}
                        {results.users.length > 0 && (
                            <div>
                                <h3 className="font-medium mb-3 flex items-center gap-2">
                                    <User className="w-4 h-4" />
                                    Users ({results.users.length})
                                </h3>
                                <div className="space-y-2">
                                    {results.users.slice(0, 3).map((user) => (
                                        <div
                                            key={user.id}
                                            onClick={() => onUserSelect?.(user)}
                                            className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                                        >
                                            <Avatar className="w-10 h-10">
                                                <AvatarImage src={user.avatar ? `/storage/${user.avatar}` : undefined} />
                                                <AvatarFallback>
                                                    {user.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                                                </AvatarFallback>
                                            </Avatar>
                                            <div>
                                                <h4 className="font-medium">{user.name}</h4>
                                                <p className="text-sm text-gray-600">{user.campus} • {user.department}</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Organizations */}
                        {results.organizations.length > 0 && (
                            <div>
                                <h3 className="font-medium mb-3 flex items-center gap-2">
                                    <Users className="w-4 h-4" />
                                    Organizations ({results.organizations.length})
                                </h3>
                                <div className="space-y-2">
                                    {results.organizations.slice(0, 3).map((org) => (
                                        <div
                                            key={org.id}
                                            onClick={() => onOrganizationSelect?.(org)}
                                            className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                                        >
                                            <h4 className="font-medium">{org.name}</h4>
                                            <p className="text-sm text-gray-600 line-clamp-2">{org.description}</p>
                                            <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
                                                <span>{org.members_count} members</span>
                                                <span>•</span>
                                                <span>{org.posts_count} posts</span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            )}

            {/* Loading State */}
            {loading && (
                <div className="mt-4 text-center py-4">
                    <div className="inline-flex items-center gap-2 text-gray-500">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500"></div>
                        Searching...
                    </div>
                </div>
            )}
        </div>
    );
}
