import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { X, Filter, RotateCcw } from 'lucide-react';

interface Filters {
    type: string;
    organization_id: string;
    campus: string;
    date_from: string;
    date_to: string;
    sort_by: string;
    sort_order: string;
}

interface Organization {
    id: number;
    name: string;
    campus: string;
}

interface FeedFiltersProps {
    filters: Filters;
    onFiltersChange: (filters: Filters) => void;
    onClose: () => void;
}

const POST_TYPES = [
    { value: 'announcement', label: 'Announcements' },
    { value: 'discussion', label: 'Discussions' },
    { value: 'event', label: 'Events' },
    { value: 'news', label: 'News' },
];

const SORT_OPTIONS = [
    { value: 'created_at', label: 'Latest' },
    { value: 'popularity', label: 'Most Popular' },
    { value: 'trending', label: 'Trending' },
    { value: 'reactions_count', label: 'Most Liked' },
    { value: 'comments_count', label: 'Most Discussed' },
];

const CAMPUSES = [
    'Main Campus',
    'Isulan Campus',
    'ACCESS Campus',
    'Tacurong Campus',
    'Bagumbayan Campus',
];

export default function FeedFilters({ filters, onFiltersChange, onClose }: FeedFiltersProps) {
    const [localFilters, setLocalFilters] = useState<Filters>(filters);
    const [organizations, setOrganizations] = useState<Organization[]>([]);
    const [loadingOrganizations, setLoadingOrganizations] = useState(false);

    // Load organizations for filter dropdown
    useEffect(() => {
        const loadOrganizations = async () => {
            setLoadingOrganizations(true);
            try {
                const response = await fetch('/api/v1/organizations?per_page=100', {
                    headers: {
                        'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                        'Accept': 'application/json',
                    }
                });
                if (response.ok) {
                    const data = await response.json();
                    setOrganizations(data.data || []);
                }
            } catch (error) {
                console.error('Error loading organizations:', error);
            } finally {
                setLoadingOrganizations(false);
            }
        };

        loadOrganizations();
    }, []);

    const handleFilterChange = (key: keyof Filters, value: string) => {
        setLocalFilters(prev => ({
            ...prev,
            [key]: value
        }));
    };

    const applyFilters = () => {
        onFiltersChange(localFilters);
        onClose();
    };

    const resetFilters = () => {
        const resetFilters: Filters = {
            type: '',
            organization_id: '',
            campus: '',
            date_from: '',
            date_to: '',
            sort_by: 'created_at',
            sort_order: 'desc'
        };
        setLocalFilters(resetFilters);
        onFiltersChange(resetFilters);
    };

    const getActiveFiltersCount = () => {
        return Object.values(localFilters).filter(value => 
            value !== '' && value !== 'created_at' && value !== 'desc'
        ).length;
    };

    const removeFilter = (key: keyof Filters) => {
        const newFilters = { ...localFilters };
        if (key === 'sort_by') {
            newFilters[key] = 'created_at';
        } else if (key === 'sort_order') {
            newFilters[key] = 'desc';
        } else {
            newFilters[key] = '';
        }
        setLocalFilters(newFilters);
    };

    return (
        <Card className="w-full">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
                <div className="flex items-center space-x-2">
                    <Filter className="w-5 h-5" />
                    <CardTitle>Filter Posts</CardTitle>
                    {getActiveFiltersCount() > 0 && (
                        <Badge variant="secondary">
                            {getActiveFiltersCount()} active
                        </Badge>
                    )}
                </div>
                <Button variant="ghost" size="sm" onClick={onClose}>
                    <X className="w-4 h-4" />
                </Button>
            </CardHeader>
            
            <CardContent className="space-y-6">
                {/* Active Filters */}
                {getActiveFiltersCount() > 0 && (
                    <div className="space-y-2">
                        <Label className="text-sm font-medium">Active Filters</Label>
                        <div className="flex flex-wrap gap-2">
                            {localFilters.type && (
                                <Badge variant="outline" className="flex items-center gap-1">
                                    Type: {POST_TYPES.find(t => t.value === localFilters.type)?.label}
                                    <button onClick={() => removeFilter('type')}>
                                        <X className="w-3 h-3" />
                                    </button>
                                </Badge>
                            )}
                            {localFilters.organization_id && (
                                <Badge variant="outline" className="flex items-center gap-1">
                                    Org: {organizations.find(o => o.id.toString() === localFilters.organization_id)?.name}
                                    <button onClick={() => removeFilter('organization_id')}>
                                        <X className="w-3 h-3" />
                                    </button>
                                </Badge>
                            )}
                            {localFilters.campus && (
                                <Badge variant="outline" className="flex items-center gap-1">
                                    Campus: {localFilters.campus}
                                    <button onClick={() => removeFilter('campus')}>
                                        <X className="w-3 h-3" />
                                    </button>
                                </Badge>
                            )}
                            {localFilters.date_from && (
                                <Badge variant="outline" className="flex items-center gap-1">
                                    From: {localFilters.date_from}
                                    <button onClick={() => removeFilter('date_from')}>
                                        <X className="w-3 h-3" />
                                    </button>
                                </Badge>
                            )}
                            {localFilters.date_to && (
                                <Badge variant="outline" className="flex items-center gap-1">
                                    To: {localFilters.date_to}
                                    <button onClick={() => removeFilter('date_to')}>
                                        <X className="w-3 h-3" />
                                    </button>
                                </Badge>
                            )}
                        </div>
                    </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Post Type Filter */}
                    <div className="space-y-2">
                        <Label htmlFor="type">Post Type</Label>
                        <Select
                            value={localFilters.type}
                            onValueChange={(value) => handleFilterChange('type', value)}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="All types" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">All types</SelectItem>
                                {POST_TYPES.map((type) => (
                                    <SelectItem key={type.value} value={type.value}>
                                        {type.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Organization Filter */}
                    <div className="space-y-2">
                        <Label htmlFor="organization">Organization</Label>
                        <Select
                            value={localFilters.organization_id}
                            onValueChange={(value) => handleFilterChange('organization_id', value)}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="All organizations" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">All organizations</SelectItem>
                                {organizations.map((org) => (
                                    <SelectItem key={org.id} value={org.id.toString()}>
                                        {org.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Campus Filter */}
                    <div className="space-y-2">
                        <Label htmlFor="campus">Campus</Label>
                        <Select
                            value={localFilters.campus}
                            onValueChange={(value) => handleFilterChange('campus', value)}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="All campuses" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">All campuses</SelectItem>
                                {CAMPUSES.map((campus) => (
                                    <SelectItem key={campus} value={campus}>
                                        {campus}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Sort By */}
                    <div className="space-y-2">
                        <Label htmlFor="sort">Sort By</Label>
                        <Select
                            value={localFilters.sort_by}
                            onValueChange={(value) => handleFilterChange('sort_by', value)}
                        >
                            <SelectTrigger>
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                {SORT_OPTIONS.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                        {option.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                {/* Date Range */}
                <div className="space-y-2">
                    <Label>Date Range</Label>
                    <div className="grid grid-cols-2 gap-2">
                        <div>
                            <Label htmlFor="date_from" className="text-xs text-gray-500">From</Label>
                            <Input
                                id="date_from"
                                type="date"
                                value={localFilters.date_from}
                                onChange={(e) => handleFilterChange('date_from', e.target.value)}
                            />
                        </div>
                        <div>
                            <Label htmlFor="date_to" className="text-xs text-gray-500">To</Label>
                            <Input
                                id="date_to"
                                type="date"
                                value={localFilters.date_to}
                                onChange={(e) => handleFilterChange('date_to', e.target.value)}
                            />
                        </div>
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-between pt-4 border-t">
                    <Button
                        variant="outline"
                        onClick={resetFilters}
                        className="flex items-center gap-2"
                    >
                        <RotateCcw className="w-4 h-4" />
                        Reset All
                    </Button>
                    
                    <div className="flex gap-2">
                        <Button variant="outline" onClick={onClose}>
                            Cancel
                        </Button>
                        <Button onClick={applyFilters}>
                            Apply Filters
                        </Button>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
