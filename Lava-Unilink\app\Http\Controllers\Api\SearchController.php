<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Post;
use App\Models\User;
use App\Models\Organization;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SearchController extends Controller
{
    /**
     * Perform global search across posts, users, and organizations.
     */
    public function global(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        $type = $request->get('type', 'all'); // all, posts, users, organizations
        $limit = $request->get('limit', 10);

        if (empty($query)) {
            return response()->json([
                'posts' => [],
                'users' => [],
                'organizations' => [],
                'total' => 0
            ]);
        }

        $results = [];

        if ($type === 'all' || $type === 'posts') {
            $results['posts'] = $this->searchPosts($query, $limit);
        }

        if ($type === 'all' || $type === 'users') {
            $results['users'] = $this->searchUsers($query, $limit);
        }

        if ($type === 'all' || $type === 'organizations') {
            $results['organizations'] = $this->searchOrganizations($query, $limit);
        }

        $results['total'] = collect($results)->sum(function ($items) {
            return is_array($items) ? count($items) : 0;
        });

        return response()->json($results);
    }

    /**
     * Advanced post search with filters.
     */
    public function posts(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        $filters = $request->only([
            'type', 'organization_id', 'campus', 'user_id',
            'date_from', 'date_to', 'has_media', 'min_reactions',
            'min_comments', 'visibility'
        ]);

        $user = Auth::user();
        $organizationIds = $user->organizations()->where('status', 'active')->pluck('id')->toArray();

        $postsQuery = Post::with(['user', 'organization', 'reactions.user'])
                         ->withCount(['comments', 'reactions']);

        // Apply visibility filters
        $postsQuery->where(function ($q) use ($organizationIds, $user) {
            $q->where('visibility', 'public')
              ->orWhere(function ($subQ) use ($organizationIds) {
                  $subQ->where('visibility', 'members_only')
                       ->whereIn('organization_id', $organizationIds);
              })
              ->orWhere('user_id', $user->id);
        });

        // Full-text search
        if (!empty($query)) {
            $postsQuery->whereRaw(
                "MATCH(title, content) AGAINST(? IN BOOLEAN MODE)",
                [$this->prepareSearchQuery($query)]
            );
        }

        // Apply filters
        $this->applyPostFilters($postsQuery, $filters);

        // Order by relevance if searching, otherwise by date
        if (!empty($query)) {
            $postsQuery->selectRaw('posts.*, MATCH(title, content) AGAINST(? IN BOOLEAN MODE) as relevance_score', [$this->prepareSearchQuery($query)])
                      ->orderByDesc('relevance_score')
                      ->orderByDesc('created_at');
        } else {
            $postsQuery->orderByDesc('is_pinned')
                      ->orderByDesc('created_at');
        }

        $posts = $postsQuery->paginate($request->get('per_page', 15));

        // Add user reaction status
        foreach ($posts as $post) {
            $post->user_reaction = $post->reactions()
                                       ->where('user_id', $user->id)
                                       ->first()?->type;
        }

        return response()->json($posts);
    }

    /**
     * Get search suggestions.
     */
    public function suggestions(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $suggestions = [];

        // Popular search terms from posts
        $postSuggestions = Post::selectRaw('title as suggestion, "post" as type, COUNT(*) as frequency')
                              ->where('title', 'LIKE', "%{$query}%")
                              ->where('visibility', 'public')
                              ->groupBy('title')
                              ->orderByDesc('frequency')
                              ->limit(5)
                              ->get();

        // Organization names
        $orgSuggestions = Organization::select('name as suggestion')
                                    ->selectRaw('"organization" as type')
                                    ->where('name', 'LIKE', "%{$query}%")
                                    ->where('status', 'active')
                                    ->limit(5)
                                    ->get();

        // User names
        $userSuggestions = User::select('name as suggestion')
                              ->selectRaw('"user" as type')
                              ->where('name', 'LIKE', "%{$query}%")
                              ->where('is_active', true)
                              ->limit(5)
                              ->get();

        $suggestions = collect()
            ->merge($postSuggestions)
            ->merge($orgSuggestions)
            ->merge($userSuggestions)
            ->take(10);

        return response()->json($suggestions);
    }

    /**
     * Get trending search terms.
     */
    public function trending(Request $request): JsonResponse
    {
        // This would typically be stored in a separate search_logs table
        // For now, we'll return trending post types and organizations
        
        $trendingTypes = Post::selectRaw('type, COUNT(*) as count')
                            ->where('created_at', '>=', now()->subDays(7))
                            ->where('visibility', 'public')
                            ->groupBy('type')
                            ->orderByDesc('count')
                            ->limit(5)
                            ->get();

        $trendingOrgs = Organization::withCount(['posts' => function ($query) {
                                      $query->where('created_at', '>=', now()->subDays(7));
                                  }])
                                  ->where('status', 'active')
                                  ->orderByDesc('posts_count')
                                  ->limit(5)
                                  ->get(['id', 'name', 'posts_count']);

        return response()->json([
            'types' => $trendingTypes,
            'organizations' => $trendingOrgs
        ]);
    }

    /**
     * Search posts with basic query.
     */
    private function searchPosts(string $query, int $limit): array
    {
        $user = Auth::user();
        $organizationIds = $user->organizations()->where('status', 'active')->pluck('id')->toArray();

        return Post::with(['user', 'organization'])
                  ->withCount(['comments', 'reactions'])
                  ->where(function ($q) use ($organizationIds, $user) {
                      $q->where('visibility', 'public')
                        ->orWhere(function ($subQ) use ($organizationIds) {
                            $subQ->where('visibility', 'members_only')
                                 ->whereIn('organization_id', $organizationIds);
                        })
                        ->orWhere('user_id', $user->id);
                  })
                  ->where(function ($q) use ($query) {
                      $q->where('title', 'LIKE', "%{$query}%")
                        ->orWhere('content', 'LIKE', "%{$query}%");
                  })
                  ->published()
                  ->orderByDesc('created_at')
                  ->limit($limit)
                  ->get()
                  ->toArray();
    }

    /**
     * Search users.
     */
    private function searchUsers(string $query, int $limit): array
    {
        return User::select(['id', 'name', 'email', 'avatar', 'campus', 'department'])
                  ->where('name', 'LIKE', "%{$query}%")
                  ->where('is_active', true)
                  ->orderBy('name')
                  ->limit($limit)
                  ->get()
                  ->toArray();
    }

    /**
     * Search organizations.
     */
    private function searchOrganizations(string $query, int $limit): array
    {
        return Organization::with(['creator'])
                          ->withCount(['members', 'posts'])
                          ->where('name', 'LIKE', "%{$query}%")
                          ->orWhere('description', 'LIKE', "%{$query}%")
                          ->where('status', 'active')
                          ->orderBy('name')
                          ->limit($limit)
                          ->get()
                          ->toArray();
    }

    /**
     * Apply filters to post query.
     */
    private function applyPostFilters($query, array $filters): void
    {
        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (!empty($filters['organization_id'])) {
            $query->where('organization_id', $filters['organization_id']);
        }

        if (!empty($filters['campus'])) {
            $query->whereHas('organization', function ($q) use ($filters) {
                $q->where('campus', $filters['campus']);
            });
        }

        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to'] . ' 23:59:59');
        }

        if (!empty($filters['has_media'])) {
            $query->whereNotNull('media');
        }

        if (!empty($filters['min_reactions'])) {
            $query->having('reactions_count', '>=', $filters['min_reactions']);
        }

        if (!empty($filters['min_comments'])) {
            $query->having('comments_count', '>=', $filters['min_comments']);
        }

        if (!empty($filters['visibility'])) {
            $query->where('visibility', $filters['visibility']);
        }
    }

    /**
     * Prepare search query for full-text search.
     */
    private function prepareSearchQuery(string $query): string
    {
        // Add wildcard to each word for partial matching
        $words = explode(' ', trim($query));
        $searchTerms = array_map(function ($word) {
            return '+' . $word . '*';
        }, $words);
        
        return implode(' ', $searchTerms);
    }
}
