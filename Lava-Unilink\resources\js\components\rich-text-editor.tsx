import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
    Bold, 
    Italic, 
    Underline, 
    List, 
    ListOrdered, 
    Link, 
    Quote,
    Code,
    Undo,
    Redo,
    Type,
    AlignLeft,
    AlignCenter,
    AlignRight
} from 'lucide-react';

interface RichTextEditorProps {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
    className?: string;
    minHeight?: string;
}

export default function RichTextEditor({ 
    value, 
    onChange, 
    placeholder = "Start writing...",
    className = "",
    minHeight = "200px"
}: RichTextEditorProps) {
    const editorRef = useRef<HTMLDivElement>(null);
    const [isEditorFocused, setIsEditorFocused] = useState(false);

    useEffect(() => {
        if (editorRef.current && editorRef.current.innerHTML !== value) {
            editorRef.current.innerHTML = value;
        }
    }, [value]);

    const executeCommand = (command: string, value?: string) => {
        document.execCommand(command, false, value);
        editorRef.current?.focus();
        updateContent();
    };

    const updateContent = () => {
        if (editorRef.current) {
            const content = editorRef.current.innerHTML;
            onChange(content);
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        // Handle keyboard shortcuts
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'b':
                    e.preventDefault();
                    executeCommand('bold');
                    break;
                case 'i':
                    e.preventDefault();
                    executeCommand('italic');
                    break;
                case 'u':
                    e.preventDefault();
                    executeCommand('underline');
                    break;
                case 'z':
                    e.preventDefault();
                    if (e.shiftKey) {
                        executeCommand('redo');
                    } else {
                        executeCommand('undo');
                    }
                    break;
            }
        }
    };

    const insertLink = () => {
        const url = prompt('Enter URL:');
        if (url) {
            executeCommand('createLink', url);
        }
    };

    const formatBlock = (tag: string) => {
        executeCommand('formatBlock', tag);
    };

    const toolbarButtons = [
        {
            group: 'format',
            buttons: [
                { icon: Bold, command: 'bold', title: 'Bold (Ctrl+B)' },
                { icon: Italic, command: 'italic', title: 'Italic (Ctrl+I)' },
                { icon: Underline, command: 'underline', title: 'Underline (Ctrl+U)' },
            ]
        },
        {
            group: 'alignment',
            buttons: [
                { icon: AlignLeft, command: 'justifyLeft', title: 'Align Left' },
                { icon: AlignCenter, command: 'justifyCenter', title: 'Align Center' },
                { icon: AlignRight, command: 'justifyRight', title: 'Align Right' },
            ]
        },
        {
            group: 'lists',
            buttons: [
                { icon: List, command: 'insertUnorderedList', title: 'Bullet List' },
                { icon: ListOrdered, command: 'insertOrderedList', title: 'Numbered List' },
            ]
        },
        {
            group: 'blocks',
            buttons: [
                { icon: Quote, command: () => formatBlock('blockquote'), title: 'Quote' },
                { icon: Code, command: () => formatBlock('pre'), title: 'Code Block' },
            ]
        },
        {
            group: 'insert',
            buttons: [
                { icon: Link, command: insertLink, title: 'Insert Link' },
            ]
        },
        {
            group: 'history',
            buttons: [
                { icon: Undo, command: 'undo', title: 'Undo (Ctrl+Z)' },
                { icon: Redo, command: 'redo', title: 'Redo (Ctrl+Shift+Z)' },
            ]
        }
    ];

    const handlePaste = (e: React.ClipboardEvent) => {
        e.preventDefault();
        const text = e.clipboardData.getData('text/plain');
        executeCommand('insertText', text);
    };

    return (
        <div className={`border rounded-lg overflow-hidden ${className}`}>
            {/* Toolbar */}
            <div className="border-b bg-gray-50 p-2">
                <div className="flex items-center gap-1 flex-wrap">
                    {toolbarButtons.map((group, groupIndex) => (
                        <div key={group.group} className="flex items-center">
                            {group.buttons.map((button, buttonIndex) => {
                                const Icon = button.icon;
                                return (
                                    <Button
                                        key={buttonIndex}
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                            if (typeof button.command === 'function') {
                                                button.command();
                                            } else {
                                                executeCommand(button.command);
                                            }
                                        }}
                                        title={button.title}
                                        className="h-8 w-8 p-0"
                                    >
                                        <Icon className="h-4 w-4" />
                                    </Button>
                                );
                            })}
                            {groupIndex < toolbarButtons.length - 1 && (
                                <Separator orientation="vertical" className="mx-1 h-6" />
                            )}
                        </div>
                    ))}
                </div>
            </div>

            {/* Editor */}
            <div className="relative">
                <div
                    ref={editorRef}
                    contentEditable
                    onInput={updateContent}
                    onFocus={() => setIsEditorFocused(true)}
                    onBlur={() => setIsEditorFocused(false)}
                    onKeyDown={handleKeyDown}
                    onPaste={handlePaste}
                    className={`
                        p-4 outline-none prose prose-sm max-w-none
                        ${!isEditorFocused && !value ? 'text-gray-500' : ''}
                    `}
                    style={{ minHeight }}
                    suppressContentEditableWarning={true}
                />
                
                {/* Placeholder */}
                {!value && !isEditorFocused && (
                    <div 
                        className="absolute top-4 left-4 text-gray-500 pointer-events-none"
                        style={{ fontSize: '14px' }}
                    >
                        {placeholder}
                    </div>
                )}
            </div>

            {/* Character Count */}
            <div className="border-t bg-gray-50 px-4 py-2 text-right">
                <span className="text-xs text-gray-500">
                    {editorRef.current?.textContent?.length || 0} characters
                </span>
            </div>

            {/* Editor Styles */}
            <style jsx>{`
                .prose blockquote {
                    border-left: 4px solid #e5e7eb;
                    padding-left: 1rem;
                    margin: 1rem 0;
                    font-style: italic;
                    color: #6b7280;
                }
                
                .prose pre {
                    background-color: #f3f4f6;
                    padding: 1rem;
                    border-radius: 0.375rem;
                    overflow-x: auto;
                    font-family: 'Courier New', monospace;
                    font-size: 0.875rem;
                }
                
                .prose ul, .prose ol {
                    padding-left: 1.5rem;
                }
                
                .prose li {
                    margin: 0.25rem 0;
                }
                
                .prose a {
                    color: #3b82f6;
                    text-decoration: underline;
                }
                
                .prose a:hover {
                    color: #1d4ed8;
                }
                
                .prose h1, .prose h2, .prose h3 {
                    font-weight: 600;
                    margin: 1rem 0 0.5rem 0;
                }
                
                .prose h1 {
                    font-size: 1.5rem;
                }
                
                .prose h2 {
                    font-size: 1.25rem;
                }
                
                .prose h3 {
                    font-size: 1.125rem;
                }
                
                .prose p {
                    margin: 0.5rem 0;
                }
                
                .prose strong {
                    font-weight: 600;
                }
                
                .prose em {
                    font-style: italic;
                }
                
                .prose u {
                    text-decoration: underline;
                }
            `}</style>
        </div>
    );
}
