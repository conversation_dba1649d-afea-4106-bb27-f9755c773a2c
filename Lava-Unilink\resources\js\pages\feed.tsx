import { useState, useEffect, useCallback } from 'react';
import { Head, Link } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import PostCard from '@/components/post-card';
import FeedFilters from '@/components/feed-filters';
import CreatePostModal from '@/components/create-post-modal';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { type BreadcrumbItem } from '@/types';
import {
    Plus,
    Search,
    Filter,
    TrendingUp,
    RefreshCw,
    Users,
    FileText
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Feed',
        href: '/feed',
    },
];

interface Post {
    id: number;
    title: string;
    content: string;
    type: string;
    visibility: string;
    is_pinned: boolean;
    comments_enabled: boolean;
    media?: Array<{
        path: string;
        name: string;
        type: string;
    }>;
    user: {
        id: number;
        name: string;
        avatar?: string;
    };
    organization?: {
        id: number;
        name: string;
        logo?: string;
    };
    comments_count: number;
    reactions_count: number;
    user_reaction?: string;
    created_at: string;
    published_at: string;
}

interface FeedProps {
    initialPosts?: {
        data: Post[];
        current_page: number;
        last_page: number;
        total: number;
    };
    feedType?: 'personalized' | 'trending' | 'all';
}

export default function Feed({ initialPosts, feedType = 'personalized' }: FeedProps) {
    const [posts, setPosts] = useState<Post[]>(initialPosts?.data || []);
    const [loading, setLoading] = useState(false);
    const [refreshing, setRefreshing] = useState(false);
    const [hasMore, setHasMore] = useState(initialPosts ? initialPosts.current_page < initialPosts.last_page : true);
    const [currentPage, setCurrentPage] = useState(initialPosts?.current_page || 1);
    const [searchQuery, setSearchQuery] = useState('');
    const [showFilters, setShowFilters] = useState(false);
    const [showCreatePost, setShowCreatePost] = useState(false);
    const [activeTab, setActiveTab] = useState<'personalized' | 'trending' | 'all'>(feedType);
    
    const [filters, setFilters] = useState({
        type: '',
        organization_id: '',
        campus: '',
        date_from: '',
        date_to: '',
        sort_by: 'created_at',
        sort_order: 'desc'
    });

    // Load posts based on active tab and filters
    const loadPosts = useCallback(async (page = 1, append = false) => {
        setLoading(true);
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                per_page: '15',
                ...(searchQuery && { search: searchQuery }),
                ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v !== ''))
            });

            let endpoint = '/api/v1/posts';
            if (activeTab === 'personalized') {
                endpoint = '/api/v1/feed';
            } else if (activeTab === 'trending') {
                endpoint = '/api/v1/feed/trending';
            }

            const response = await fetch(`${endpoint}?${params}`, {
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'Accept': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (append) {
                    setPosts(prev => [...prev, ...data.data]);
                } else {
                    setPosts(data.data);
                }
                setCurrentPage(data.current_page);
                setHasMore(data.current_page < data.last_page);
            }
        } catch (error) {
            console.error('Error loading posts:', error);
        } finally {
            setLoading(false);
        }
    }, [activeTab, searchQuery, filters]);

    // Load more posts for infinite scroll
    const loadMore = useCallback(() => {
        if (!loading && hasMore) {
            loadPosts(currentPage + 1, true);
        }
    }, [loading, hasMore, currentPage, loadPosts]);

    // Refresh feed
    const refreshFeed = useCallback(async () => {
        setRefreshing(true);
        await loadPosts(1, false);
        setRefreshing(false);
    }, [loadPosts]);

    // Handle search
    const handleSearch = useCallback((e: React.FormEvent) => {
        e.preventDefault();
        loadPosts(1, false);
    }, [loadPosts]);

    // Handle filter changes
    const handleFilterChange = useCallback((newFilters: typeof filters) => {
        setFilters(newFilters);
        loadPosts(1, false);
    }, [loadPosts]);

    // Handle tab change
    const handleTabChange = useCallback((tab: 'personalized' | 'trending' | 'all') => {
        setActiveTab(tab);
        setCurrentPage(1);
        setPosts([]);
    }, []);

    // Load posts when tab changes
    useEffect(() => {
        loadPosts(1, false);
    }, [activeTab]);

    // Infinite scroll effect
    useEffect(() => {
        const handleScroll = () => {
            if (window.innerHeight + document.documentElement.scrollTop !== document.documentElement.offsetHeight || loading) {
                return;
            }
            loadMore();
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, [loadMore, loading]);

    // Handle post reactions
    const handleReaction = async (postId: number, reactionType: string) => {
        try {
            const response = await fetch('/api/v1/reactions/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    reactable_type: 'post',
                    reactable_id: postId,
                    type: reactionType,
                }),
            });

            if (response.ok) {
                setPosts(prevPosts =>
                    prevPosts.map(post =>
                        post.id === postId
                            ? { 
                                ...post, 
                                reactions_count: post.user_reaction === reactionType 
                                    ? post.reactions_count - 1 
                                    : post.reactions_count + (post.user_reaction ? 0 : 1),
                                user_reaction: post.user_reaction === reactionType ? undefined : reactionType
                            }
                            : post
                    )
                );
            }
        } catch (error) {
            console.error('Error toggling reaction:', error);
        }
    };

    const handleComment = (postId: number) => {
        window.location.href = `/posts/${postId}#comments`;
    };

    const handleShare = (postId: number) => {
        // Implement share functionality
        navigator.share?.({
            title: 'UniLink Post',
            url: `${window.location.origin}/posts/${postId}`
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Feed" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Feed</h1>
                        <p className="text-gray-600">Stay updated with the latest posts from your community</p>
                    </div>
                    
                    <div className="flex items-center gap-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={refreshFeed}
                            disabled={refreshing}
                        >
                            <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                            Refresh
                        </Button>
                        
                        <Button onClick={() => setShowCreatePost(true)}>
                            <Plus className="w-4 h-4 mr-2" />
                            Create Post
                        </Button>
                    </div>
                </div>

                {/* Feed Tabs */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                        <button
                            onClick={() => handleTabChange('personalized')}
                            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                                activeTab === 'personalized'
                                    ? 'bg-white text-gray-900 shadow-sm'
                                    : 'text-gray-600 hover:text-gray-900'
                            }`}
                        >
                            For You
                        </button>
                        <button
                            onClick={() => handleTabChange('trending')}
                            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                                activeTab === 'trending'
                                    ? 'bg-white text-gray-900 shadow-sm'
                                    : 'text-gray-600 hover:text-gray-900'
                            }`}
                        >
                            <TrendingUp className="w-4 h-4 mr-1" />
                            Trending
                        </button>
                        <button
                            onClick={() => handleTabChange('all')}
                            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                                activeTab === 'all'
                                    ? 'bg-white text-gray-900 shadow-sm'
                                    : 'text-gray-600 hover:text-gray-900'
                            }`}
                        >
                            All Posts
                        </button>
                    </div>

                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowFilters(!showFilters)}
                    >
                        <Filter className="w-4 h-4 mr-2" />
                        Filters
                    </Button>
                </div>

                {/* Search and Filters */}
                <div className="space-y-4">
                    {/* Search Bar */}
                    <form onSubmit={handleSearch} className="flex gap-2">
                        <div className="flex-1 relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                            <Input
                                type="text"
                                placeholder="Search posts, users, organizations..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="pl-10"
                            />
                        </div>
                        <Button type="submit">Search</Button>
                    </form>

                    {/* Filters Panel */}
                    {showFilters && (
                        <FeedFilters
                            filters={filters}
                            onFiltersChange={handleFilterChange}
                            onClose={() => setShowFilters(false)}
                        />
                    )}
                </div>

                {/* Posts Feed */}
                <div className="space-y-6">
                    {posts.length > 0 ? (
                        posts.map((post) => (
                            <PostCard
                                key={post.id}
                                post={post}
                                onReact={handleReaction}
                                onComment={handleComment}
                                onShare={handleShare}
                            />
                        ))
                    ) : !loading ? (
                        <Card>
                            <CardContent className="p-8 text-center">
                                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No posts found</h3>
                                <p className="text-gray-600 mb-4">
                                    {activeTab === 'personalized' 
                                        ? "Join some organizations to see personalized content in your feed."
                                        : "No posts match your current filters."
                                    }
                                </p>
                                <Link href="/organizations">
                                    <Button>
                                        <Users className="w-4 h-4 mr-2" />
                                        Browse Organizations
                                    </Button>
                                </Link>
                            </CardContent>
                        </Card>
                    ) : null}

                    {/* Loading Skeletons */}
                    {loading && (
                        <div className="space-y-6">
                            {[...Array(3)].map((_, i) => (
                                <Card key={i}>
                                    <div className="p-6 space-y-4">
                                        <div className="flex items-center space-x-3">
                                            <Skeleton className="w-10 h-10 rounded-full" />
                                            <div className="space-y-2">
                                                <Skeleton className="h-4 w-32" />
                                                <Skeleton className="h-3 w-24" />
                                            </div>
                                        </div>
                                        <Skeleton className="h-6 w-3/4" />
                                        <Skeleton className="h-20 w-full" />
                                        <div className="flex space-x-4">
                                            <Skeleton className="h-8 w-16" />
                                            <Skeleton className="h-8 w-16" />
                                            <Skeleton className="h-8 w-16" />
                                        </div>
                                    </div>
                                </Card>
                            ))}
                        </div>
                    )}
                </div>
            </div>

            {/* Create Post Modal */}
            {showCreatePost && (
                <CreatePostModal
                    onClose={() => setShowCreatePost(false)}
                    onPostCreated={(newPost) => {
                        setPosts(prev => [newPost, ...prev]);
                        setShowCreatePost(false);
                    }}
                />
            )}
        </AppLayout>
    );
}
