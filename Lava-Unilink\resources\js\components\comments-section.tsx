import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { 
    MessageCircle, 
    Heart, 
    Reply, 
    Edit, 
    Trash2, 
    Send,
    ChevronDown,
    ChevronUp,
    MoreHorizontal
} from 'lucide-react';

interface User {
    id: number;
    name: string;
    avatar?: string;
}

interface Comment {
    id: number;
    content: string;
    user: User;
    created_at: string;
    updated_at: string;
    is_edited: boolean;
    parent_id?: number;
    replies?: Comment[];
    reactions_count: number;
    user_reaction?: string;
    can_edit: boolean;
    can_delete: boolean;
}

interface CommentsSectionProps {
    postId: number;
    comments: Comment[];
    currentUser: User;
    onCommentAdded?: (comment: Comment) => void;
    onCommentUpdated?: (comment: Comment) => void;
    onCommentDeleted?: (commentId: number) => void;
}

export default function CommentsSection({
    postId,
    comments: initialComments,
    currentUser,
    onCommentAdded,
    onCommentUpdated,
    onCommentDeleted
}: CommentsSectionProps) {
    const [comments, setComments] = useState<Comment[]>(initialComments);
    const [newComment, setNewComment] = useState('');
    const [replyingTo, setReplyingTo] = useState<number | null>(null);
    const [editingComment, setEditingComment] = useState<number | null>(null);
    const [editContent, setEditContent] = useState('');
    const [expandedReplies, setExpandedReplies] = useState<Set<number>>(new Set());
    const [loading, setLoading] = useState(false);

    // Load comments
    const loadComments = async () => {
        try {
            const response = await fetch(`/api/v1/posts/${postId}/comments`, {
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'Accept': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                setComments(data);
            }
        } catch (error) {
            console.error('Error loading comments:', error);
        }
    };

    // Add new comment
    const handleAddComment = async (content: string, parentId?: number) => {
        if (!content.trim()) return;

        setLoading(true);
        try {
            const response = await fetch('/api/v1/comments', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    content,
                    post_id: postId,
                    parent_id: parentId
                })
            });

            if (response.ok) {
                const newCommentData = await response.json();
                
                if (parentId) {
                    // Add as reply
                    setComments(prev => prev.map(comment => 
                        comment.id === parentId 
                            ? { ...comment, replies: [...(comment.replies || []), newCommentData.comment] }
                            : comment
                    ));
                    setExpandedReplies(prev => new Set([...prev, parentId]));
                } else {
                    // Add as top-level comment
                    setComments(prev => [newCommentData.comment, ...prev]);
                }

                setNewComment('');
                setReplyingTo(null);
                onCommentAdded?.(newCommentData.comment);
            }
        } catch (error) {
            console.error('Error adding comment:', error);
        } finally {
            setLoading(false);
        }
    };

    // Edit comment
    const handleEditComment = async (commentId: number, content: string) => {
        if (!content.trim()) return;

        try {
            const response = await fetch(`/api/v1/comments/${commentId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ content })
            });

            if (response.ok) {
                const updatedComment = await response.json();
                
                setComments(prev => prev.map(comment => {
                    if (comment.id === commentId) {
                        return { ...comment, ...updatedComment.comment };
                    }
                    if (comment.replies) {
                        return {
                            ...comment,
                            replies: comment.replies.map(reply => 
                                reply.id === commentId ? { ...reply, ...updatedComment.comment } : reply
                            )
                        };
                    }
                    return comment;
                }));

                setEditingComment(null);
                setEditContent('');
                onCommentUpdated?.(updatedComment.comment);
            }
        } catch (error) {
            console.error('Error editing comment:', error);
        }
    };

    // Delete comment
    const handleDeleteComment = async (commentId: number) => {
        if (!confirm('Are you sure you want to delete this comment?')) return;

        try {
            const response = await fetch(`/api/v1/comments/${commentId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                }
            });

            if (response.ok) {
                setComments(prev => prev.filter(comment => {
                    if (comment.id === commentId) return false;
                    if (comment.replies) {
                        comment.replies = comment.replies.filter(reply => reply.id !== commentId);
                    }
                    return true;
                }));

                onCommentDeleted?.(commentId);
            }
        } catch (error) {
            console.error('Error deleting comment:', error);
        }
    };

    // React to comment
    const handleReaction = async (commentId: number, reactionType: string) => {
        try {
            const response = await fetch('/api/v1/reactions/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    reactable_type: 'comment',
                    reactable_id: commentId,
                    type: reactionType,
                }),
            });

            if (response.ok) {
                setComments(prev => prev.map(comment => {
                    if (comment.id === commentId) {
                        return {
                            ...comment,
                            reactions_count: comment.user_reaction === reactionType 
                                ? comment.reactions_count - 1 
                                : comment.reactions_count + (comment.user_reaction ? 0 : 1),
                            user_reaction: comment.user_reaction === reactionType ? undefined : reactionType
                        };
                    }
                    if (comment.replies) {
                        return {
                            ...comment,
                            replies: comment.replies.map(reply => 
                                reply.id === commentId 
                                    ? {
                                        ...reply,
                                        reactions_count: reply.user_reaction === reactionType 
                                            ? reply.reactions_count - 1 
                                            : reply.reactions_count + (reply.user_reaction ? 0 : 1),
                                        user_reaction: reply.user_reaction === reactionType ? undefined : reactionType
                                    }
                                    : reply
                            )
                        };
                    }
                    return comment;
                }));
            }
        } catch (error) {
            console.error('Error toggling reaction:', error);
        }
    };

    const toggleReplies = (commentId: number) => {
        setExpandedReplies(prev => {
            const newSet = new Set(prev);
            if (newSet.has(commentId)) {
                newSet.delete(commentId);
            } else {
                newSet.add(commentId);
            }
            return newSet;
        });
    };

    const CommentItem = ({ comment, isReply = false }: { comment: Comment; isReply?: boolean }) => (
        <div className={`${isReply ? 'ml-8 border-l-2 border-gray-100 pl-4' : ''}`}>
            <div className="flex items-start gap-3">
                <Avatar className="w-8 h-8">
                    <AvatarImage src={comment.user.avatar ? `/storage/${comment.user.avatar}` : undefined} />
                    <AvatarFallback>
                        {comment.user.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </AvatarFallback>
                </Avatar>

                <div className="flex-1 min-w-0">
                    <div className="bg-gray-50 rounded-lg p-3">
                        <div className="flex items-center justify-between mb-1">
                            <span className="font-medium text-sm">{comment.user.name}</span>
                            <div className="flex items-center gap-2">
                                {comment.is_edited && (
                                    <Badge variant="outline" className="text-xs">Edited</Badge>
                                )}
                                <span className="text-xs text-gray-500">
                                    {new Date(comment.created_at).toLocaleString()}
                                </span>
                            </div>
                        </div>

                        {editingComment === comment.id ? (
                            <div className="space-y-2">
                                <Textarea
                                    value={editContent}
                                    onChange={(e) => setEditContent(e.target.value)}
                                    rows={3}
                                />
                                <div className="flex gap-2">
                                    <Button 
                                        size="sm" 
                                        onClick={() => handleEditComment(comment.id, editContent)}
                                    >
                                        Save
                                    </Button>
                                    <Button 
                                        size="sm" 
                                        variant="outline"
                                        onClick={() => {
                                            setEditingComment(null);
                                            setEditContent('');
                                        }}
                                    >
                                        Cancel
                                    </Button>
                                </div>
                            </div>
                        ) : (
                            <p className="text-sm text-gray-700 whitespace-pre-wrap">{comment.content}</p>
                        )}
                    </div>

                    {/* Comment Actions */}
                    <div className="flex items-center gap-4 mt-2 text-sm">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleReaction(comment.id, 'like')}
                            className={`h-auto p-1 ${comment.user_reaction === 'like' ? 'text-red-600' : 'text-gray-500'}`}
                        >
                            <Heart className={`w-4 h-4 mr-1 ${comment.user_reaction === 'like' ? 'fill-current' : ''}`} />
                            {comment.reactions_count}
                        </Button>

                        {!isReply && (
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                                className="h-auto p-1 text-gray-500"
                            >
                                <Reply className="w-4 h-4 mr-1" />
                                Reply
                            </Button>
                        )}

                        {comment.can_edit && (
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                    setEditingComment(comment.id);
                                    setEditContent(comment.content);
                                }}
                                className="h-auto p-1 text-gray-500"
                            >
                                <Edit className="w-4 h-4" />
                            </Button>
                        )}

                        {comment.can_delete && (
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteComment(comment.id)}
                                className="h-auto p-1 text-red-500"
                            >
                                <Trash2 className="w-4 h-4" />
                            </Button>
                        )}
                    </div>

                    {/* Reply Form */}
                    {replyingTo === comment.id && (
                        <div className="mt-3 space-y-2">
                            <Textarea
                                placeholder="Write a reply..."
                                value={newComment}
                                onChange={(e) => setNewComment(e.target.value)}
                                rows={3}
                            />
                            <div className="flex gap-2">
                                <Button 
                                    size="sm"
                                    onClick={() => handleAddComment(newComment, comment.id)}
                                    disabled={!newComment.trim() || loading}
                                >
                                    <Send className="w-4 h-4 mr-1" />
                                    Reply
                                </Button>
                                <Button 
                                    size="sm" 
                                    variant="outline"
                                    onClick={() => {
                                        setReplyingTo(null);
                                        setNewComment('');
                                    }}
                                >
                                    Cancel
                                </Button>
                            </div>
                        </div>
                    )}

                    {/* Replies */}
                    {comment.replies && comment.replies.length > 0 && (
                        <div className="mt-3">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleReplies(comment.id)}
                                className="h-auto p-1 text-blue-600"
                            >
                                {expandedReplies.has(comment.id) ? (
                                    <>
                                        <ChevronUp className="w-4 h-4 mr-1" />
                                        Hide {comment.replies.length} replies
                                    </>
                                ) : (
                                    <>
                                        <ChevronDown className="w-4 h-4 mr-1" />
                                        Show {comment.replies.length} replies
                                    </>
                                )}
                            </Button>

                            {expandedReplies.has(comment.id) && (
                                <div className="mt-3 space-y-3">
                                    {comment.replies.map(reply => (
                                        <CommentItem key={reply.id} comment={reply} isReply={true} />
                                    ))}
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <MessageCircle className="w-5 h-5" />
                    Comments ({comments.length})
                </CardTitle>
            </CardHeader>
            
            <CardContent className="space-y-6">
                {/* Add Comment Form */}
                <div className="space-y-3">
                    <Textarea
                        placeholder="Write a comment..."
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        rows={3}
                    />
                    <Button 
                        onClick={() => handleAddComment(newComment)}
                        disabled={!newComment.trim() || loading}
                    >
                        <Send className="w-4 h-4 mr-2" />
                        Post Comment
                    </Button>
                </div>

                {/* Comments List */}
                <div className="space-y-6">
                    {comments.length > 0 ? (
                        comments.map(comment => (
                            <CommentItem key={comment.id} comment={comment} />
                        ))
                    ) : (
                        <div className="text-center py-8 text-gray-500">
                            <MessageCircle className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                            <p>No comments yet. Be the first to comment!</p>
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}
