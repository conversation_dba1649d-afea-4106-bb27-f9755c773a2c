import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
    Heart, 
    MessageCircle, 
    Share2, 
    Eye, 
    TrendingUp, 
    Users,
    Calendar,
    Award,
    Target,
    Zap
} from 'lucide-react';

interface EngagementData {
    total_reactions: number;
    total_comments: number;
    total_shares: number;
    total_views: number;
    posts_count: number;
    followers_count: number;
    engagement_rate: number;
    trending_score: number;
    recent_activity: {
        reactions: number;
        comments: number;
        shares: number;
        views: number;
    };
    top_performing_posts: Array<{
        id: number;
        title: string;
        engagement_score: number;
        reactions_count: number;
        comments_count: number;
    }>;
    achievements: Array<{
        id: string;
        name: string;
        description: string;
        icon: string;
        earned_at: string;
        rarity: 'common' | 'rare' | 'epic' | 'legendary';
    }>;
}

interface EngagementStatsProps {
    userId?: number;
    timeframe?: 'week' | 'month' | 'year' | 'all';
    showAchievements?: boolean;
}

export default function EngagementStats({ 
    userId, 
    timeframe = 'month',
    showAchievements = true 
}: EngagementStatsProps) {
    const [data, setData] = useState<EngagementData | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        loadEngagementData();
    }, [userId, timeframe]);

    const loadEngagementData = async () => {
        setLoading(true);
        try {
            const params = new URLSearchParams({
                timeframe,
                ...(userId && { user_id: userId.toString() })
            });

            const response = await fetch(`/api/v1/engagement/stats?${params}`, {
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'Accept': 'application/json',
                }
            });

            if (response.ok) {
                const engagementData = await response.json();
                setData(engagementData);
            }
        } catch (error) {
            console.error('Error loading engagement data:', error);
        } finally {
            setLoading(false);
        }
    };

    const getEngagementLevel = (rate: number) => {
        if (rate >= 80) return { level: 'Excellent', color: 'text-green-600', bg: 'bg-green-100' };
        if (rate >= 60) return { level: 'Good', color: 'text-blue-600', bg: 'bg-blue-100' };
        if (rate >= 40) return { level: 'Average', color: 'text-yellow-600', bg: 'bg-yellow-100' };
        return { level: 'Needs Improvement', color: 'text-red-600', bg: 'bg-red-100' };
    };

    const getAchievementRarityColor = (rarity: string) => {
        switch (rarity) {
            case 'legendary': return 'border-yellow-400 bg-yellow-50 text-yellow-800';
            case 'epic': return 'border-purple-400 bg-purple-50 text-purple-800';
            case 'rare': return 'border-blue-400 bg-blue-50 text-blue-800';
            default: return 'border-gray-400 bg-gray-50 text-gray-800';
        }
    };

    if (loading) {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {[...Array(4)].map((_, i) => (
                    <Card key={i} className="animate-pulse">
                        <CardContent className="p-6">
                            <div className="h-4 bg-gray-200 rounded mb-2"></div>
                            <div className="h-8 bg-gray-200 rounded"></div>
                        </CardContent>
                    </Card>
                ))}
            </div>
        );
    }

    if (!data) {
        return (
            <Card>
                <CardContent className="p-6 text-center">
                    <p className="text-gray-500">No engagement data available</p>
                </CardContent>
            </Card>
        );
    }

    const engagementLevel = getEngagementLevel(data.engagement_rate);

    return (
        <div className="space-y-6">
            {/* Main Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total Reactions</p>
                                <p className="text-2xl font-bold">{data.total_reactions.toLocaleString()}</p>
                                <p className="text-xs text-green-600">
                                    +{data.recent_activity.reactions} this {timeframe}
                                </p>
                            </div>
                            <Heart className="w-8 h-8 text-red-500" />
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total Comments</p>
                                <p className="text-2xl font-bold">{data.total_comments.toLocaleString()}</p>
                                <p className="text-xs text-blue-600">
                                    +{data.recent_activity.comments} this {timeframe}
                                </p>
                            </div>
                            <MessageCircle className="w-8 h-8 text-blue-500" />
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total Shares</p>
                                <p className="text-2xl font-bold">{data.total_shares.toLocaleString()}</p>
                                <p className="text-xs text-purple-600">
                                    +{data.recent_activity.shares} this {timeframe}
                                </p>
                            </div>
                            <Share2 className="w-8 h-8 text-purple-500" />
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total Views</p>
                                <p className="text-2xl font-bold">{data.total_views.toLocaleString()}</p>
                                <p className="text-xs text-orange-600">
                                    +{data.recent_activity.views} this {timeframe}
                                </p>
                            </div>
                            <Eye className="w-8 h-8 text-orange-500" />
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Engagement Overview */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <TrendingUp className="w-5 h-5" />
                            Engagement Overview
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">Engagement Rate</span>
                            <Badge className={`${engagementLevel.bg} ${engagementLevel.color} border-0`}>
                                {engagementLevel.level}
                            </Badge>
                        </div>
                        <Progress value={data.engagement_rate} className="h-2" />
                        <p className="text-xs text-gray-600">
                            {data.engagement_rate}% of your audience engages with your content
                        </p>

                        <div className="grid grid-cols-2 gap-4 pt-4">
                            <div className="text-center">
                                <p className="text-lg font-bold">{data.posts_count}</p>
                                <p className="text-xs text-gray-600">Posts Created</p>
                            </div>
                            <div className="text-center">
                                <p className="text-lg font-bold">{data.trending_score}</p>
                                <p className="text-xs text-gray-600">Trending Score</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Target className="w-5 h-5" />
                            Top Performing Posts
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            {data.top_performing_posts.length > 0 ? (
                                data.top_performing_posts.slice(0, 3).map((post, index) => (
                                    <div key={post.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div className="flex-1 min-w-0">
                                            <p className="text-sm font-medium truncate">{post.title}</p>
                                            <div className="flex items-center gap-3 mt-1 text-xs text-gray-600">
                                                <span className="flex items-center gap-1">
                                                    <Heart className="w-3 h-3" />
                                                    {post.reactions_count}
                                                </span>
                                                <span className="flex items-center gap-1">
                                                    <MessageCircle className="w-3 h-3" />
                                                    {post.comments_count}
                                                </span>
                                            </div>
                                        </div>
                                        <Badge variant="outline">
                                            #{index + 1}
                                        </Badge>
                                    </div>
                                ))
                            ) : (
                                <p className="text-sm text-gray-500 text-center py-4">
                                    No posts data available
                                </p>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Achievements */}
            {showAchievements && data.achievements.length > 0 && (
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Award className="w-5 h-5" />
                            Recent Achievements
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {data.achievements.slice(0, 6).map((achievement) => (
                                <div
                                    key={achievement.id}
                                    className={`p-4 rounded-lg border-2 ${getAchievementRarityColor(achievement.rarity)}`}
                                >
                                    <div className="flex items-center gap-3">
                                        <div className="text-2xl">{achievement.icon}</div>
                                        <div className="flex-1 min-w-0">
                                            <h4 className="font-medium text-sm">{achievement.name}</h4>
                                            <p className="text-xs opacity-80">{achievement.description}</p>
                                            <p className="text-xs mt-1">
                                                Earned {new Date(achievement.earned_at).toLocaleDateString()}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Engagement Tips */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Zap className="w-5 h-5" />
                        Engagement Tips
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <h4 className="font-medium text-sm">Boost Your Reach</h4>
                            <ul className="text-xs text-gray-600 space-y-1">
                                <li>• Post during peak hours (10 AM - 2 PM)</li>
                                <li>• Use relevant hashtags and mentions</li>
                                <li>• Include engaging visuals or media</li>
                                <li>• Ask questions to encourage comments</li>
                            </ul>
                        </div>
                        <div className="space-y-2">
                            <h4 className="font-medium text-sm">Content Strategy</h4>
                            <ul className="text-xs text-gray-600 space-y-1">
                                <li>• Share valuable and informative content</li>
                                <li>• Respond to comments promptly</li>
                                <li>• Collaborate with organizations</li>
                                <li>• Share behind-the-scenes content</li>
                            </ul>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
