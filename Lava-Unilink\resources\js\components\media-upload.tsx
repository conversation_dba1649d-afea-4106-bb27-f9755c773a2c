import { useState, useRef, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
    Upload, 
    X, 
    Image, 
    FileText, 
    Video, 
    Music,
    File,
    Eye,
    Download,
    RotateCcw
} from 'lucide-react';

interface MediaFile {
    file: File;
    id: string;
    preview?: string;
    uploadProgress?: number;
    uploaded?: boolean;
    error?: string;
}

interface MediaUploadProps {
    files: MediaFile[];
    onFilesChange: (files: MediaFile[]) => void;
    maxFiles?: number;
    maxFileSize?: number; // in MB
    acceptedTypes?: string[];
    showPreview?: boolean;
    allowMultiple?: boolean;
}

const DEFAULT_ACCEPTED_TYPES = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'video/mp4',
    'video/webm',
    'audio/mp3',
    'audio/wav',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
];

export default function MediaUpload({
    files,
    onFilesChange,
    maxFiles = 10,
    maxFileSize = 10, // 10MB
    acceptedTypes = DEFAULT_ACCEPTED_TYPES,
    showPreview = true,
    allowMultiple = true
}: MediaUploadProps) {
    const [dragOver, setDragOver] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const generateId = () => Math.random().toString(36).substr(2, 9);

    const getFileIcon = (file: File) => {
        if (file.type.startsWith('image/')) return Image;
        if (file.type.startsWith('video/')) return Video;
        if (file.type.startsWith('audio/')) return Music;
        if (file.type === 'application/pdf') return FileText;
        return File;
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const validateFile = (file: File): string | null => {
        if (!acceptedTypes.includes(file.type)) {
            return `File type ${file.type} is not supported`;
        }
        
        if (file.size > maxFileSize * 1024 * 1024) {
            return `File size must be less than ${maxFileSize}MB`;
        }
        
        if (files.length >= maxFiles) {
            return `Maximum ${maxFiles} files allowed`;
        }
        
        return null;
    };

    const createPreview = (file: File): Promise<string | undefined> => {
        return new Promise((resolve) => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => resolve(e.target?.result as string);
                reader.readAsDataURL(file);
            } else {
                resolve(undefined);
            }
        });
    };

    const processFiles = useCallback(async (fileList: FileList) => {
        const newFiles: MediaFile[] = [];
        
        for (let i = 0; i < fileList.length; i++) {
            const file = fileList[i];
            const error = validateFile(file);
            
            if (error) {
                // Show error notification
                console.error(error);
                continue;
            }
            
            const preview = await createPreview(file);
            
            newFiles.push({
                file,
                id: generateId(),
                preview,
                uploadProgress: 0,
                uploaded: false
            });
        }
        
        if (allowMultiple) {
            onFilesChange([...files, ...newFiles]);
        } else {
            onFilesChange(newFiles.slice(0, 1));
        }
    }, [files, onFilesChange, allowMultiple, maxFiles, maxFileSize, acceptedTypes]);

    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const fileList = e.target.files;
        if (fileList) {
            processFiles(fileList);
        }
        // Reset input value to allow selecting the same file again
        e.target.value = '';
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setDragOver(false);
        
        const fileList = e.dataTransfer.files;
        if (fileList) {
            processFiles(fileList);
        }
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        setDragOver(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setDragOver(false);
    };

    const removeFile = (id: string) => {
        onFilesChange(files.filter(f => f.id !== id));
    };

    const retryUpload = (id: string) => {
        const updatedFiles = files.map(f => 
            f.id === id 
                ? { ...f, error: undefined, uploadProgress: 0, uploaded: false }
                : f
        );
        onFilesChange(updatedFiles);
    };

    const openFileDialog = () => {
        fileInputRef.current?.click();
    };

    return (
        <div className="space-y-4">
            {/* Upload Area */}
            <div
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                className={`
                    border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer
                    ${dragOver 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-300 hover:border-gray-400'
                    }
                `}
                onClick={openFileDialog}
            >
                <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600 mb-1">
                    Drop files here or click to browse
                </p>
                <p className="text-xs text-gray-500">
                    Max {maxFiles} files, {maxFileSize}MB each
                </p>
                
                <input
                    ref={fileInputRef}
                    type="file"
                    multiple={allowMultiple}
                    accept={acceptedTypes.join(',')}
                    onChange={handleFileSelect}
                    className="hidden"
                />
            </div>

            {/* File List */}
            {files.length > 0 && (
                <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700">
                        Uploaded Files ({files.length}/{maxFiles})
                    </h4>
                    
                    <div className="grid grid-cols-1 gap-2">
                        {files.map((mediaFile) => {
                            const Icon = getFileIcon(mediaFile.file);
                            
                            return (
                                <Card key={mediaFile.id} className="p-3">
                                    <div className="flex items-center gap-3">
                                        {/* File Icon/Preview */}
                                        <div className="flex-shrink-0">
                                            {showPreview && mediaFile.preview ? (
                                                <img
                                                    src={mediaFile.preview}
                                                    alt={mediaFile.file.name}
                                                    className="w-12 h-12 object-cover rounded"
                                                />
                                            ) : (
                                                <div className="w-12 h-12 bg-gray-100 rounded flex items-center justify-center">
                                                    <Icon className="w-6 h-6 text-gray-500" />
                                                </div>
                                            )}
                                        </div>
                                        
                                        {/* File Info */}
                                        <div className="flex-1 min-w-0">
                                            <p className="text-sm font-medium text-gray-900 truncate">
                                                {mediaFile.file.name}
                                            </p>
                                            <div className="flex items-center gap-2 mt-1">
                                                <span className="text-xs text-gray-500">
                                                    {formatFileSize(mediaFile.file.size)}
                                                </span>
                                                <Badge variant="outline" className="text-xs">
                                                    {mediaFile.file.type.split('/')[1]?.toUpperCase()}
                                                </Badge>
                                            </div>
                                            
                                            {/* Upload Progress */}
                                            {mediaFile.uploadProgress !== undefined && !mediaFile.uploaded && !mediaFile.error && (
                                                <Progress 
                                                    value={mediaFile.uploadProgress} 
                                                    className="mt-2 h-1"
                                                />
                                            )}
                                            
                                            {/* Error Message */}
                                            {mediaFile.error && (
                                                <p className="text-xs text-red-600 mt-1">
                                                    {mediaFile.error}
                                                </p>
                                            )}
                                        </div>
                                        
                                        {/* Actions */}
                                        <div className="flex items-center gap-1">
                                            {mediaFile.uploaded && (
                                                <Badge variant="default" className="text-xs">
                                                    Uploaded
                                                </Badge>
                                            )}
                                            
                                            {mediaFile.error && (
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => retryUpload(mediaFile.id)}
                                                    className="h-8 w-8 p-0"
                                                >
                                                    <RotateCcw className="w-4 h-4" />
                                                </Button>
                                            )}
                                            
                                            {showPreview && mediaFile.preview && (
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => window.open(mediaFile.preview, '_blank')}
                                                    className="h-8 w-8 p-0"
                                                >
                                                    <Eye className="w-4 h-4" />
                                                </Button>
                                            )}
                                            
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => removeFile(mediaFile.id)}
                                                className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                                            >
                                                <X className="w-4 h-4" />
                                            </Button>
                                        </div>
                                    </div>
                                </Card>
                            );
                        })}
                    </div>
                </div>
            )}
        </div>
    );
}
