<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add fulltext indexes for better search performance
        DB::statement('ALTER TABLE posts ADD FULLTEXT(title, content)');
        
        // Add indexes for common filter columns
        Schema::table('posts', function (Blueprint $table) {
            $table->index(['type', 'visibility', 'created_at']);
            $table->index(['organization_id', 'visibility', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['is_pinned', 'created_at']);
            $table->index(['published_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove fulltext index
        DB::statement('ALTER TABLE posts DROP INDEX title');
        
        // Remove other indexes
        Schema::table('posts', function (Blueprint $table) {
            $table->dropIndex(['type', 'visibility', 'created_at']);
            $table->dropIndex(['organization_id', 'visibility', 'created_at']);
            $table->dropIndex(['user_id', 'created_at']);
            $table->dropIndex(['is_pinned', 'created_at']);
            $table->dropIndex(['published_at']);
        });
    }
};
