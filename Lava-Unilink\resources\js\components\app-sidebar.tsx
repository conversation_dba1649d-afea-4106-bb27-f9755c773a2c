import { AppLogo } from '@/components/app-logo';
import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Link } from '@inertiajs/react';
import { Bell, Calendar, Home, MessageSquare, Settings, Users, Rss } from 'lucide-react';

export function AppSidebar() {
    return (
        <aside className="fixed inset-y-0 left-0 z-20 hidden w-64 flex-col border-r bg-unilink-lightest dark:bg-unilink-darkest dark:border-unilink-secondary md:flex">
            <div className="flex h-16 items-center border-b px-6 dark:border-unilink-secondary">
                <AppLogo className="h-8 w-8 text-unilink-primary" />
                <span className="ml-2 text-xl font-semibold text-unilink-darkest dark:text-unilink-lightest">UniLink</span>
            </div>
            <nav className="flex-1 space-y-1 px-3 py-4">
                <SidebarLink href="/dashboard" icon={Home} label="Dashboard" />
                <SidebarLink href="/feed" icon={Rss} label="Feed" />
                <SidebarLink href="/announcements" icon={Bell} label="Announcements" />
                <SidebarLink href="/events" icon={Calendar} label="Events" />
                <SidebarLink href="/organizations" icon={Users} label="Organizations" />
                <SidebarLink href="/messages" icon={MessageSquare} label="Messages" />
                <SidebarLink href="/settings" icon={Settings} label="Settings" />
            </nav>
        </aside>
    );
}

interface SidebarLinkProps {
    href: string;
    icon: React.ElementType;
    label: string;
}

function SidebarLink({ href, icon: Icon, label }: SidebarLinkProps) {
    const isActive = window.location.pathname.startsWith(href);
    
    return (
        <Link href={href}>
            <Button
                variant="ghost"
                className={cn(
                    'w-full justify-start gap-2 text-unilink-secondary dark:text-unilink-lightest',
                    isActive && 'bg-unilink-primary/10 text-unilink-primary dark:bg-unilink-primary/20'
                )}
            >
                <Icon className="h-5 w-5" />
                {label}
            </Button>
        </Link>
    );
}

