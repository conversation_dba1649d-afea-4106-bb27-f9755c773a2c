import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    Plus, 
    Search, 
    Filter, 
    Edit, 
    Trash2, 
    Eye, 
    Calendar,
    Users,
    MessageCircle,
    Heart,
    Pin,
    Globe,
    Lock,
    MoreHorizontal
} from 'lucide-react';
import { type BreadcrumbItem } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Posts', href: '/posts' },
    { title: 'Manage', href: '/posts/manage' },
];

interface Post {
    id: number;
    title: string;
    content: string;
    type: string;
    visibility: string;
    is_pinned: boolean;
    comments_enabled: boolean;
    user: {
        id: number;
        name: string;
        avatar?: string;
    };
    organization?: {
        id: number;
        name: string;
        logo?: string;
    };
    comments_count: number;
    reactions_count: number;
    created_at: string;
    published_at: string;
    status: 'published' | 'draft' | 'scheduled';
}

export default function ManagePosts() {
    const [posts, setPosts] = useState<Post[]>([]);
    const [loading, setLoading] = useState(true);
    const [searchQuery, setSearchQuery] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [typeFilter, setTypeFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);

    // Load user's posts
    const loadPosts = async (page = 1) => {
        setLoading(true);
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                per_page: '10',
                user_id: 'current', // Backend should handle this
                ...(searchQuery && { search: searchQuery }),
                ...(statusFilter !== 'all' && { status: statusFilter }),
                ...(typeFilter !== 'all' && { type: typeFilter }),
            });

            const response = await fetch(`/api/v1/posts?${params}`, {
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'Accept': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                setPosts(data.data);
                setCurrentPage(data.current_page);
                setTotalPages(data.last_page);
            }
        } catch (error) {
            console.error('Error loading posts:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadPosts(1);
    }, [searchQuery, statusFilter, typeFilter]);

    const handleDelete = async (postId: number) => {
        if (!confirm('Are you sure you want to delete this post?')) {
            return;
        }

        try {
            const response = await fetch(`/api/v1/posts/${postId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                }
            });

            if (response.ok) {
                setPosts(posts.filter(p => p.id !== postId));
            }
        } catch (error) {
            console.error('Error deleting post:', error);
        }
    };

    const togglePin = async (postId: number, isPinned: boolean) => {
        try {
            const response = await fetch(`/api/v1/posts/${postId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ is_pinned: !isPinned })
            });

            if (response.ok) {
                setPosts(posts.map(p => 
                    p.id === postId ? { ...p, is_pinned: !isPinned } : p
                ));
            }
        } catch (error) {
            console.error('Error updating post:', error);
        }
    };

    const getStatusBadge = (post: Post) => {
        if (post.published_at && new Date(post.published_at) > new Date()) {
            return <Badge variant="outline">Scheduled</Badge>;
        }
        if (post.published_at) {
            return <Badge variant="default">Published</Badge>;
        }
        return <Badge variant="secondary">Draft</Badge>;
    };

    const getVisibilityIcon = (visibility: string) => {
        switch (visibility) {
            case 'public': return <Globe className="w-4 h-4" />;
            case 'members_only': return <Users className="w-4 h-4" />;
            case 'private': return <Lock className="w-4 h-4" />;
            default: return <Globe className="w-4 h-4" />;
        }
    };

    const truncateContent = (content: string, maxLength = 100) => {
        const textContent = content.replace(/<[^>]*>/g, ''); // Strip HTML
        return textContent.length > maxLength 
            ? textContent.substring(0, maxLength) + '...'
            : textContent;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Manage Posts" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Manage Posts</h1>
                        <p className="text-gray-600">Create, edit, and manage your posts</p>
                    </div>
                    
                    <Link href="/posts/create">
                        <Button>
                            <Plus className="w-4 h-4 mr-2" />
                            Create Post
                        </Button>
                    </Link>
                </div>

                {/* Filters */}
                <Card>
                    <CardContent className="p-4">
                        <div className="flex flex-col sm:flex-row gap-4">
                            {/* Search */}
                            <div className="flex-1 relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                <Input
                                    type="text"
                                    placeholder="Search posts..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="pl-10"
                                />
                            </div>

                            {/* Status Filter */}
                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger className="w-40">
                                    <SelectValue placeholder="Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Status</SelectItem>
                                    <SelectItem value="published">Published</SelectItem>
                                    <SelectItem value="draft">Draft</SelectItem>
                                    <SelectItem value="scheduled">Scheduled</SelectItem>
                                </SelectContent>
                            </Select>

                            {/* Type Filter */}
                            <Select value={typeFilter} onValueChange={setTypeFilter}>
                                <SelectTrigger className="w-40">
                                    <SelectValue placeholder="Type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Types</SelectItem>
                                    <SelectItem value="announcement">Announcement</SelectItem>
                                    <SelectItem value="discussion">Discussion</SelectItem>
                                    <SelectItem value="event">Event</SelectItem>
                                    <SelectItem value="news">News</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </CardContent>
                </Card>

                {/* Posts List */}
                <div className="space-y-4">
                    {loading ? (
                        <div className="text-center py-8">
                            <div className="inline-flex items-center gap-2 text-gray-500">
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500"></div>
                                Loading posts...
                            </div>
                        </div>
                    ) : posts.length > 0 ? (
                        posts.map((post) => (
                            <Card key={post.id} className="hover:shadow-md transition-shadow">
                                <CardContent className="p-6">
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1 min-w-0">
                                            {/* Title and Status */}
                                            <div className="flex items-center gap-3 mb-2">
                                                <h3 className="text-lg font-semibold text-gray-900 truncate">
                                                    {post.title}
                                                </h3>
                                                {post.is_pinned && (
                                                    <Pin className="w-4 h-4 text-blue-600" />
                                                )}
                                                {getStatusBadge(post)}
                                                <Badge variant="outline" className="capitalize">
                                                    {post.type}
                                                </Badge>
                                            </div>

                                            {/* Content Preview */}
                                            <p className="text-gray-600 mb-3">
                                                {truncateContent(post.content)}
                                            </p>

                                            {/* Meta Info */}
                                            <div className="flex items-center gap-4 text-sm text-gray-500">
                                                <div className="flex items-center gap-1">
                                                    {getVisibilityIcon(post.visibility)}
                                                    <span className="capitalize">{post.visibility.replace('_', ' ')}</span>
                                                </div>
                                                
                                                <div className="flex items-center gap-1">
                                                    <Heart className="w-4 h-4" />
                                                    <span>{post.reactions_count}</span>
                                                </div>
                                                
                                                <div className="flex items-center gap-1">
                                                    <MessageCircle className="w-4 h-4" />
                                                    <span>{post.comments_count}</span>
                                                </div>
                                                
                                                <div className="flex items-center gap-1">
                                                    <Calendar className="w-4 h-4" />
                                                    <span>{new Date(post.created_at).toLocaleDateString()}</span>
                                                </div>

                                                {post.organization && (
                                                    <div className="flex items-center gap-1">
                                                        <Users className="w-4 h-4" />
                                                        <span>{post.organization.name}</span>
                                                    </div>
                                                )}
                                            </div>
                                        </div>

                                        {/* Actions */}
                                        <div className="flex items-center gap-2 ml-4">
                                            <Link href={`/posts/${post.id}`}>
                                                <Button variant="ghost" size="sm">
                                                    <Eye className="w-4 h-4" />
                                                </Button>
                                            </Link>
                                            
                                            <Link href={`/posts/${post.id}/edit`}>
                                                <Button variant="ghost" size="sm">
                                                    <Edit className="w-4 h-4" />
                                                </Button>
                                            </Link>
                                            
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => togglePin(post.id, post.is_pinned)}
                                                className={post.is_pinned ? 'text-blue-600' : ''}
                                            >
                                                <Pin className="w-4 h-4" />
                                            </Button>
                                            
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleDelete(post.id)}
                                                className="text-red-600 hover:text-red-700"
                                            >
                                                <Trash2 className="w-4 h-4" />
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))
                    ) : (
                        <Card>
                            <CardContent className="p-8 text-center">
                                <div className="text-gray-400 mb-4">
                                    <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                </div>
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No posts found</h3>
                                <p className="text-gray-600 mb-4">
                                    You haven't created any posts yet. Start sharing your thoughts with the community!
                                </p>
                                <Link href="/posts/create">
                                    <Button>
                                        <Plus className="w-4 h-4 mr-2" />
                                        Create Your First Post
                                    </Button>
                                </Link>
                            </CardContent>
                        </Card>
                    )}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                    <div className="flex justify-center gap-2">
                        <Button
                            variant="outline"
                            onClick={() => loadPosts(currentPage - 1)}
                            disabled={currentPage === 1}
                        >
                            Previous
                        </Button>
                        
                        <span className="flex items-center px-4 text-sm text-gray-600">
                            Page {currentPage} of {totalPages}
                        </span>
                        
                        <Button
                            variant="outline"
                            onClick={() => loadPosts(currentPage + 1)}
                            disabled={currentPage === totalPages}
                        >
                            Next
                        </Button>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
