import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
    Share2, 
    Copy, 
    Facebook, 
    Twitter, 
    Linkedin, 
    Mail, 
    MessageCircle,
    Link as LinkIcon,
    Check,
    QrCode,
    Download
} from 'lucide-react';

interface Post {
    id: number;
    title: string;
    content: string;
    user: {
        name: string;
    };
    organization?: {
        name: string;
    };
}

interface ShareModalProps {
    post: Post;
    isOpen: boolean;
    onClose: () => void;
}

export default function ShareModal({ post, isOpen, onClose }: ShareModalProps) {
    const [copied, setCopied] = useState(false);
    const [customMessage, setCustomMessage] = useState('');
    const [shareCount, setShareCount] = useState(0);

    const postUrl = `${window.location.origin}/posts/${post.id}`;
    const shareText = `Check out this post: "${post.title}" by ${post.user.name}${post.organization ? ` from ${post.organization.name}` : ''}`;

    const copyToClipboard = async (text: string) => {
        try {
            await navigator.clipboard.writeText(text);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (error) {
            console.error('Failed to copy:', error);
        }
    };

    const shareViaEmail = () => {
        const subject = encodeURIComponent(`UniLink: ${post.title}`);
        const body = encodeURIComponent(`${shareText}\n\n${postUrl}\n\n${customMessage}`);
        window.open(`mailto:?subject=${subject}&body=${body}`);
        trackShare('email');
    };

    const shareViaFacebook = () => {
        const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(postUrl)}`;
        window.open(url, '_blank', 'width=600,height=400');
        trackShare('facebook');
    };

    const shareViaTwitter = () => {
        const text = encodeURIComponent(`${shareText} ${postUrl}`);
        const url = `https://twitter.com/intent/tweet?text=${text}`;
        window.open(url, '_blank', 'width=600,height=400');
        trackShare('twitter');
    };

    const shareViaLinkedIn = () => {
        const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(postUrl)}`;
        window.open(url, '_blank', 'width=600,height=400');
        trackShare('linkedin');
    };

    const shareViaNativeAPI = async () => {
        if (navigator.share) {
            try {
                await navigator.share({
                    title: post.title,
                    text: shareText,
                    url: postUrl,
                });
                trackShare('native');
            } catch (error) {
                console.error('Error sharing:', error);
            }
        }
    };

    const trackShare = async (platform: string) => {
        try {
            await fetch('/api/v1/posts/share', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    post_id: post.id,
                    platform,
                    custom_message: customMessage
                })
            });
            setShareCount(prev => prev + 1);
        } catch (error) {
            console.error('Error tracking share:', error);
        }
    };

    const generateQRCode = () => {
        // This would typically use a QR code library
        const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(postUrl)}`;
        window.open(qrUrl, '_blank');
    };

    const shareOptions = [
        {
            name: 'Copy Link',
            icon: Copy,
            action: () => copyToClipboard(postUrl),
            color: 'text-gray-600',
            description: 'Copy link to clipboard'
        },
        {
            name: 'Facebook',
            icon: Facebook,
            action: shareViaFacebook,
            color: 'text-blue-600',
            description: 'Share on Facebook'
        },
        {
            name: 'Twitter',
            icon: Twitter,
            action: shareViaTwitter,
            color: 'text-blue-400',
            description: 'Share on Twitter'
        },
        {
            name: 'LinkedIn',
            icon: Linkedin,
            action: shareViaLinkedIn,
            color: 'text-blue-700',
            description: 'Share on LinkedIn'
        },
        {
            name: 'Email',
            icon: Mail,
            action: shareViaEmail,
            color: 'text-green-600',
            description: 'Share via email'
        },
    ];

    // Add native share if supported
    if (navigator.share) {
        shareOptions.unshift({
            name: 'Share',
            icon: Share2,
            action: shareViaNativeAPI,
            color: 'text-purple-600',
            description: 'Use device share menu'
        });
    }

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-w-md">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <Share2 className="w-5 h-5" />
                        Share Post
                    </DialogTitle>
                </DialogHeader>

                <div className="space-y-6">
                    {/* Post Preview */}
                    <Card>
                        <CardContent className="p-4">
                            <h4 className="font-medium text-sm mb-1">{post.title}</h4>
                            <p className="text-xs text-gray-600 mb-2">
                                by {post.user.name}
                                {post.organization && ` • ${post.organization.name}`}
                            </p>
                            <p className="text-sm text-gray-700 line-clamp-3">
                                {post.content.replace(/<[^>]*>/g, '')}
                            </p>
                        </CardContent>
                    </Card>

                    {/* Custom Message */}
                    <div className="space-y-2">
                        <label className="text-sm font-medium">Add a message (optional)</label>
                        <Textarea
                            placeholder="Add your thoughts about this post..."
                            value={customMessage}
                            onChange={(e) => setCustomMessage(e.target.value)}
                            rows={3}
                        />
                    </div>

                    {/* Share Options */}
                    <div className="space-y-3">
                        <h4 className="text-sm font-medium">Share via</h4>
                        <div className="grid grid-cols-2 gap-3">
                            {shareOptions.map((option) => {
                                const Icon = option.icon;
                                return (
                                    <Button
                                        key={option.name}
                                        variant="outline"
                                        onClick={option.action}
                                        className="h-auto p-4 flex flex-col items-center gap-2"
                                    >
                                        <Icon className={`w-6 h-6 ${option.color}`} />
                                        <span className="text-xs">{option.name}</span>
                                    </Button>
                                );
                            })}
                        </div>
                    </div>

                    {/* Direct Link */}
                    <div className="space-y-2">
                        <label className="text-sm font-medium">Direct Link</label>
                        <div className="flex gap-2">
                            <Input
                                value={postUrl}
                                readOnly
                                className="text-sm"
                            />
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyToClipboard(postUrl)}
                                className="flex-shrink-0"
                            >
                                {copied ? (
                                    <Check className="w-4 h-4 text-green-600" />
                                ) : (
                                    <Copy className="w-4 h-4" />
                                )}
                            </Button>
                        </div>
                        {copied && (
                            <p className="text-xs text-green-600">Link copied to clipboard!</p>
                        )}
                    </div>

                    {/* Additional Options */}
                    <div className="flex justify-between items-center pt-4 border-t">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={generateQRCode}
                            className="flex items-center gap-2"
                        >
                            <QrCode className="w-4 h-4" />
                            QR Code
                        </Button>

                        {shareCount > 0 && (
                            <Badge variant="outline">
                                Shared {shareCount} times
                            </Badge>
                        )}
                    </div>

                    {/* Share Analytics */}
                    <div className="text-center">
                        <p className="text-xs text-gray-500">
                            Sharing helps spread knowledge within the UniLink community
                        </p>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}
